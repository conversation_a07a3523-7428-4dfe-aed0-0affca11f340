#!/bin/bash

# Exit on any error
set -e

echo "Starting Perplexity Agent..."

# Start Xvfb (X Virtual Framebuffer)
echo "Starting Xvfb..."
Xvfb :1 -screen 0 1920x1080x16 &
sleep 2

# Start window manager
echo "Starting Fluxbox window manager..."
fluxbox -display :1 &
sleep 2

# Start VNC server
echo "Starting VNC server..."
x11vnc -display :1 -forever -create -shared -rfbport 5900 -passwd secret &
sleep 2

# Start Redis server
echo "Starting Redis server..."
redis-server --daemonize yes --bind 127.0.0.1 --port 6379
sleep 2

# Wait for Redis to be ready
echo "Waiting for <PERSON>is to be ready..."
until redis-cli ping; do
  echo "Redis is unavailable - sleeping"
  sleep 1
done
echo "Redis is ready!"

# Create logs directory
mkdir -p /app/logs

# Start the FastAPI application
echo "Starting FastAPI application..."
cd /app
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
