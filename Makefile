.PHONY: help build run stop clean logs test test-german health vnc

# Default target
help:
	@echo "Perplexity Agent - Available commands:"
	@echo ""
	@echo "  build       - Build the Docker image"
	@echo "  run         - Run the container with docker-compose"
	@echo "  stop        - Stop the running container"
	@echo "  restart     - Restart the container"
	@echo "  clean       - Remove container and images"
	@echo "  logs        - Show container logs"
	@echo "  test        - Run API tests"
	@echo "  test-german - Run German localization tests"
	@echo "  health      - Check service health"
	@echo "  vnc         - Show VNC connection info"
	@echo "  shell       - Open shell in running container"
	@echo ""

# Build the Docker image
build:
	@echo "Building Perplexity Agent Docker image..."
	docker-compose build

# Run the container
run:
	@echo "Starting Perplexity Agent..."
	docker-compose up -d
	@echo "Services started!"
	@echo "API: http://localhost:8000"
	@echo "VNC: localhost:5900 (password: secret)"
	@echo "Docs: http://localhost:8000/docs"

# Stop the container
stop:
	@echo "Stopping Perplexity Agent..."
	docker-compose down

# Restart the container
restart: stop run

# Clean up containers and images
clean:
	@echo "Cleaning up..."
	docker-compose down -v --rmi all
	docker system prune -f

# Show logs
logs:
	docker-compose logs -f

# Run tests
test:
	@echo "Running API tests..."
	python test_api.py

# Run German localization tests
test-german:
	@echo "Running German localization tests..."
	python test_german_localization.py

# Check health
health:
	@echo "Checking service health..."
	curl -s http://localhost:8000/health | python -m json.tool

# Show VNC info
vnc:
	@echo "VNC Connection Information:"
	@echo "  Host: localhost"
	@echo "  Port: 5900"
	@echo "  Password: secret"
	@echo ""
	@echo "Connect using any VNC client, e.g.:"
	@echo "  vncviewer localhost:5900"

# Open shell in container
shell:
	docker-compose exec perplexity-agent /bin/bash

# Quick setup
setup: build run
	@echo "Waiting for services to start..."
	@sleep 10
	@make health

# Development mode (with auto-reload)
dev:
	@echo "Starting in development mode..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Show status
status:
	@echo "Container status:"
	@docker-compose ps
	@echo ""
	@echo "Health check:"
	@curl -s http://localhost:8000/health 2>/dev/null | python -m json.tool || echo "Service not responding"
