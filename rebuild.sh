#!/bin/bash

echo "🔄 Rebuilding Perplexity Agent with German Localization..."

# Stop and remove existing containers
echo "Stopping existing containers..."
docker-compose down -v

# Remove existing images to force rebuild
echo "Removing existing images..."
docker rmi $(docker images | grep perplexity-agent | awk '{print $3}') 2>/dev/null || true
docker rmi $(docker images | grep bellingbyte-perplexigate | awk '{print $3}') 2>/dev/null || true

# Clean up any dangling images
echo "Cleaning up dangling images..."
docker image prune -f

# Rebuild with no cache
echo "Building new image with German localization..."
docker-compose build --no-cache

# Start the services
echo "Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "Waiting for services to start..."
sleep 15

# Check health
echo "Checking service health..."
curl -s http://localhost:8000/health | python3 -m json.tool 2>/dev/null || echo "Service not ready yet"

echo ""
echo "🎉 Rebuild complete!"
echo "Services:"
echo "  API: http://localhost:8000"
echo "  VNC: localhost:5900 (password: secret)"
echo "  Docs: http://localhost:8000/docs"
echo ""
echo "Test German localization:"
echo "  make test-german"
echo ""
echo "Or test manually:"
echo '  curl -X POST "http://localhost:8000/query" -H "Content-Type: application/json" -d '"'"'{"query": "Was ist die Hauptstadt von Deutschland?"}'"'"''
