import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError
from browser_manager import browser_manager
from session_manager import session_manager
from anti_detection import AntiDetectionManager
from config import settings

logger = logging.getLogger(__name__)


class PerplexityAutomation:
    """Handles Perplexity.ai web automation and query execution."""
    
    def __init__(self):
        self.anti_detection = AntiDetectionManager()
        
    async def execute_query(self, session_id: str, query: str, use_deep_search: bool = False, 
                          sources: List[str] = None, timeout: int = 30) -> Dict[str, Any]:
        """Execute a query on Perplexity.ai and return results."""
        start_time = time.time()
        page = None
        
        try:
            # Update session status
            await session_manager.update_session(session_id, {"status": "processing"})
            
            # Create browser tab
            page = await browser_manager.create_tab(session_id)
            
            # Navigate to Perplexity
            logger.info(f"Navigating to Perplexity for session {session_id}")
            await page.goto(settings.perplexity_url, timeout=settings.browser_timeout)
            
            # Wait for page to load
            await page.wait_for_load_state("networkidle", timeout=settings.browser_timeout)
            
            # Handle potential authentication or cookie banners
            await self._handle_initial_page_setup(page)
            
            # Perform random mouse movement to mimic human behavior
            await self.anti_detection.random_mouse_movement(page)
            
            # Input query with human-like typing
            await self._input_query(page, query)
            
            # Configure advanced options if requested
            if use_deep_search:
                await self._enable_deep_search(page)
                
            if sources:
                await self._configure_sources(page, sources)
                
            # Submit query
            await self._submit_query(page)
            
            # Wait for and extract results
            result = await self._extract_results(page, timeout)
            
            execution_time = time.time() - start_time
            
            # Update session with success
            await session_manager.update_session(session_id, {
                "status": "completed",
                "execution_time": execution_time
            })
            
            logger.info(f"Query completed for session {session_id} in {execution_time:.2f}s")
            
            return {
                "result": result["answer"],
                "sources": result["sources"],
                "execution_time": execution_time,
                "session_id": session_id
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Query failed for session {session_id}: {str(e)}"
            logger.error(error_msg)
            
            # Update session with error
            await session_manager.update_session(session_id, {
                "status": "failed",
                "error": str(e),
                "execution_time": execution_time
            })
            
            raise Exception(error_msg)
            
        finally:
            # Clean up browser tab
            if page:
                await browser_manager.close_tab(session_id)
                
    async def _handle_initial_page_setup(self, page: Page) -> None:
        """Handle initial page setup like cookie banners, login prompts, etc."""
        try:
            # Wait a bit for any overlays to appear
            await asyncio.sleep(2)
            
            # Check for cookie banner and accept if present
            cookie_selectors = [
                'button[data-testid="accept-cookies"]',
                'button:has-text("Accept")',
                'button:has-text("Accept All")',
                '[data-testid="cookie-banner"] button'
            ]
            
            for selector in cookie_selectors:
                try:
                    if await page.is_visible(selector, timeout=2000):
                        await page.click(selector)
                        await self.anti_detection.random_delay(1, 2)
                        break
                except:
                    continue
                    
            # Check for any modal dialogs and close them
            modal_selectors = [
                '[role="dialog"] button[aria-label="Close"]',
                '.modal button[aria-label="Close"]',
                '[data-testid="modal-close"]'
            ]
            
            for selector in modal_selectors:
                try:
                    if await page.is_visible(selector, timeout=2000):
                        await page.click(selector)
                        await self.anti_detection.random_delay(1, 2)
                        break
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"Initial page setup handling: {e}")
            
    async def _input_query(self, page: Page, query: str) -> None:
        """Input query with human-like typing behavior."""
        try:
            # Wait for query input to be available
            await page.wait_for_selector(settings.query_selector, timeout=settings.browser_timeout)
            
            # Clear any existing text
            await page.click(settings.query_selector)
            await page.keyboard.press("Control+a")
            await self.anti_detection.random_delay(0.2, 0.5)
            
            # Type query with human-like behavior
            await self.anti_detection.human_like_typing(page, settings.query_selector, query)
            
            logger.debug(f"Query input completed: {query[:50]}...")
            
        except Exception as e:
            logger.error(f"Failed to input query: {e}")
            raise
            
    async def _enable_deep_search(self, page: Page) -> None:
        """Enable deep search/Pro search if available."""
        try:
            # Look for Pro search toggle
            if await page.is_visible(settings.pro_search_toggle, timeout=5000):
                await page.click(settings.pro_search_toggle)
                await self.anti_detection.random_delay(1, 2)
                logger.debug("Deep search enabled")
            else:
                logger.debug("Deep search toggle not found or not available")
                
        except Exception as e:
            logger.debug(f"Could not enable deep search: {e}")
            
    async def _configure_sources(self, page: Page, sources: List[str]) -> None:
        """Configure specific sources if available."""
        try:
            # This is a placeholder for source configuration
            # Implementation would depend on Perplexity's UI for source selection
            logger.debug(f"Source configuration requested: {sources}")
            
            # Look for source configuration options
            source_selectors = [
                '[data-testid="source-selector"]',
                'button:has-text("Sources")',
                '[aria-label*="source"]'
            ]
            
            for selector in source_selectors:
                try:
                    if await page.is_visible(selector, timeout=3000):
                        await page.click(selector)
                        await self.anti_detection.random_delay(1, 2)
                        # Additional source selection logic would go here
                        break
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"Could not configure sources: {e}")
            
    async def _submit_query(self, page: Page) -> None:
        """Submit the query."""
        try:
            # Submit query
            await page.click(settings.submit_selector)
            await self.anti_detection.random_delay(2, 4)
            
            logger.debug("Query submitted")
            
        except Exception as e:
            logger.error(f"Failed to submit query: {e}")
            raise
            
    async def _extract_results(self, page: Page, timeout: int) -> Dict[str, Any]:
        """Extract results from Perplexity response."""
        try:
            # Wait for answer container to appear
            await page.wait_for_selector(settings.answer_selector, timeout=timeout * 1000)
            
            # Wait a bit more for content to fully load
            await asyncio.sleep(3)
            
            # Extract answer text
            answer_element = await page.query_selector(settings.answer_selector)
            answer = await answer_element.inner_text() if answer_element else "No answer found"
            
            # Extract sources
            sources = await self._extract_sources(page)
            
            logger.debug(f"Results extracted: {len(answer)} chars, {len(sources)} sources")
            
            return {
                "answer": answer,
                "sources": sources
            }
            
        except PlaywrightTimeoutError:
            logger.error("Timeout waiting for results")
            raise Exception("Timeout waiting for Perplexity response")
        except Exception as e:
            logger.error(f"Failed to extract results: {e}")
            raise
            
    async def _extract_sources(self, page: Page) -> List[Dict[str, str]]:
        """Extract source information from the page."""
        sources = []
        
        try:
            # Common selectors for sources
            source_selectors = [
                '[data-testid="source"]',
                '.source-item',
                'a[href*="http"]',
                '[class*="source"]'
            ]
            
            for selector in source_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    for element in elements[:10]:  # Limit to first 10 sources
                        try:
                            title = await element.get_attribute("title") or await element.inner_text()
                            url = await element.get_attribute("href")
                            
                            if url and url.startswith("http"):
                                sources.append({
                                    "title": title[:100] if title else "Unknown",
                                    "url": url
                                })
                        except:
                            continue
                            
                    if sources:
                        break
                        
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"Could not extract sources: {e}")
            
        return sources


# Global automation instance
perplexity_automation = PerplexityAutomation()
