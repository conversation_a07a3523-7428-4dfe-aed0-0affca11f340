FROM mcr.microsoft.com/playwright/python:v1.40.0-jammy

# Install VNC and desktop environment
RUN apt-get update && apt-get install -y \
    xvfb \
    x11vnc \
    fluxbox \
    wget \
    wmctrl \
    redis-server \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Set up VNC password
RUN mkdir ~/.vnc && echo "secret" | vncpasswd -f > ~/.vnc/passwd && chmod 600 ~/.vnc/passwd

# Create app directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright browsers with stealth patches
RUN playwright install chromium
RUN playwright install-deps

# Copy application code
COPY . .

# Create chrome profile directory
RUN mkdir -p /app/chrome_profile && chmod 755 /app/chrome_profile

# Set up environment
ENV DISPLAY=:1
ENV PYTHONPATH=/app
ENV REDIS_URL=redis://localhost:6379

# Expose ports
EXPOSE 5900 8000 6379

# Make start script executable
RUN chmod +x start.sh

# Start script
CMD ["./start.sh"]
