import random
import asyncio
from typing import Dict, Any
from playwright.async_api import Page, Browser<PERSON>ontext
from playwright_stealth import stealth_async
from fake_useragent import UserAgent
from config import settings


class AntiDetectionManager:
    """Handles anti-bot detection measures for browser automation."""
    
    def __init__(self):
        self.ua = UserAgent()
        
    async def apply_stealth_mode(self, context: BrowserContext) -> None:
        """Apply stealth patches to browser context."""
        await stealth_async(context)
        
    def get_random_user_agent(self) -> str:
        """Get a random user agent string."""
        return random.choice(settings.user_agents)
        
    def get_browser_args(self) -> list:
        """Get browser launch arguments for anti-detection."""
        return [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-blink-features=AutomationControlled',
            f'--user-data-dir={settings.chrome_profile_path}',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-field-trial-config',
            '--disable-ipc-flooding-protection',
            '--no-first-run',
            '--no-default-browser-check',
            '--no-pings',
            '--password-store=basic',
            '--use-mock-keychain'
        ]
        
    def get_context_options(self) -> Dict[str, Any]:
        """Get browser context options for anti-detection."""
        return {
            'user_agent': self.get_random_user_agent(),
            'viewport': {'width': 1920, 'height': 1080},
            'locale': 'en-US',
            'timezone_id': 'America/New_York',
            'permissions': ['geolocation'],
            'geolocation': {'latitude': 40.7128, 'longitude': -74.0060},  # New York
            'extra_http_headers': {
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        }
        
    async def random_delay(self, min_delay: float = None, max_delay: float = None) -> None:
        """Add random delay to mimic human behavior."""
        min_d = min_delay or settings.min_delay
        max_d = max_delay or settings.max_delay
        delay = random.uniform(min_d, max_d)
        await asyncio.sleep(delay)
        
    async def human_like_typing(self, page: Page, selector: str, text: str) -> None:
        """Type text with human-like delays."""
        await page.click(selector)
        await self.random_delay(0.5, 1.0)
        
        for char in text:
            await page.keyboard.type(char)
            await asyncio.sleep(random.uniform(0.05, settings.typing_delay))
            
    async def random_mouse_movement(self, page: Page) -> None:
        """Perform random mouse movements to mimic human behavior."""
        for _ in range(random.randint(1, 3)):
            x = random.randint(100, 1800)
            y = random.randint(100, 1000)
            await page.mouse.move(x, y)
            await self.random_delay(0.1, 0.5)
            
    async def scroll_randomly(self, page: Page) -> None:
        """Perform random scrolling to mimic human behavior."""
        scroll_amount = random.randint(100, 500)
        await page.mouse.wheel(0, scroll_amount)
        await self.random_delay(0.5, 1.5)
