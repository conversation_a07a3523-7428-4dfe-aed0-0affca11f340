version: '3.8'

services:
  perplexity-agent:
    build: .
    ports:
      - "8000:8000"  # FastAPI
      - "5900:5900"  # VNC
      - "6379:6379"  # Redis
    volumes:
      - ./chrome_profile:/app/chrome_profile:rw
      - ./logs:/app/logs:rw
    environment:
      - DISPLAY=:1
      - REDIS_URL=redis://localhost:6379
      - VNC_PASSWORD=secret
      - PYTHONPATH=/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
